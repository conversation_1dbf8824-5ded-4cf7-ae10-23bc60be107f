import ast
import os

def calculate_precision(tp, fp):
    """计算精确率"""
    return tp / (tp + fp) if (tp + fp) > 0 else 0.0

def calculate_recall(tp, fn):
    """计算召回率"""
    return tp / (tp + fn) if (tp + fn) > 0 else 0.0

def calculate_f1(precision, recall):
    """计算F1值"""
    return 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

# 读取文件并解析数据
def parse_file(filename):
    data = []
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                data.append([])
                continue
            try:
                parsed_line = ast.literal_eval(line)
                if isinstance(parsed_line, list):
                    normalized = []
                    for item in parsed_line:
                        if isinstance(item, list):
                            normalized.append(item)
                    data.append(normalized)
                else:
                    data.append([])
            except (SyntaxError, ValueError):
                data.append([])
    return data

# 主计算逻辑
def main(result_path, gold_path):
    # 解析两个文件
    pred_data = parse_file(result_path)
    true_data = parse_file(gold_path)
    
    # 确保pred_data长度与true_data一致（不足补空列表）
    if len(pred_data) < len(true_data):
        pred_data += [[] for _ in range(len(true_data) - len(pred_data))]
    
    total_tp = 0
    total_fp = 0
    total_fn = 0
    
    # 计算指标
    for preds, truths in zip(pred_data, true_data):
        pred_set = {tuple(item) for item in preds}
        truth_set = {tuple(item) for item in truths}
        
        # <1>计算当前行的TP/FP/FN
        tp = _____
        fp = _____
        fn = _____
        
        total_tp += tp
        total_fp += fp
        total_fn += fn
    
    # 计算全局指标
    precision = calculate_precision(total_tp, total_fp)
    recall = calculate_recall(total_tp, total_fn)
    f1 = calculate_f1(precision, recall)
    
    # 准备结果字符串
    result_str = f"全局统计: TP={total_tp} | FP={total_fp} | FN={total_fn}\n"
    result_str += f"精确率(Precision): {precision:.4f}\n"
    result_str += f"召回率(Recall): {recall:.4f}\n"
    result_str += f"F1值: {f1:.4f}\n"
    
    # 打印结果
    print(result_str)

    # 保存结果到文件
    main_result_path = os.path.join('..', 'data', 'main_result.txt')
    with open(main_result_path, 'w', encoding='utf-8') as result_file:
        result_file.write(result_str)
    
    print("结果已保存到 main_result.txt")


if __name__ == "__main__":
    result_path = os.path.join('..', 'data', 'result_kg.txt')
    gold_path = os.path.join('..', 'data', 'target.txt')
    main(result_path,gold_path)
    
