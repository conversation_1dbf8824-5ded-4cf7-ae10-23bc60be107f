import json
import os


# 1.数据处理
def jsonl_to_txt(jsonl_file_path, text_output_path, triple_output_path):
    """
    将JSONL文件转换为两个TXT文件：
    - text_output_path: 每行存储"text"字段内容
    - triple_output_path: 每行存储"triple_list"的JSON字符串
    """
    try:
        with open(jsonl_file_path, 'r', encoding='utf-8') as jsonl_file, \
                open(text_output_path, 'w', encoding='utf-8') as text_file, \
                open(triple_output_path, 'w', encoding='utf-8') as triple_file:

            line_count = 0
            for line in jsonl_file:
                # <1>解析JSON数据
                data = json._____

                # <1>提取text字段
                text_content = data._____
                text_file.write(text_content + "\n")

                # <1>提取triple_list字段并序列化为JSON字符串
                triple_list = data._____
                triple_file.write(_____)
                line_count += 1
                if line_count % 1000 == 0:  # 每处理1000行打印进度
                    print(f"已处理 {line_count} 行...")

            print(f"转换完成！共处理 {line_count} 行数据")
            print(f"文本文件保存至: {os.path.abspath(text_output_path)}")
            print(f"三元组文件保存至: {os.path.abspath(triple_output_path)}")

    except FileNotFoundError:
        print(f"错误: 文件 {jsonl_file_path} 不存在")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"未知错误: {e}")

# 示例用法
if __name__ == "__main__":
    jsonl_to_txt(
        jsonl_file_path="CMeIE-V2_test_triples.jsonl",  # 输入的JSONL文件路径
        text_output_path="source.txt",  # 输出的文本文件路径
        triple_output_path="target.txt"  # 输出的三元组文件路径
    )