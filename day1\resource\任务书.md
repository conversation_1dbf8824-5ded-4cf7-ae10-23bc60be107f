
中华人民共和国第三届职业技能大赛
人工智能工程技术任务书

模块A、自然语言处理技术的应用

一、任务要求
根据项目要求完成人工智能自然语言处理技术应用代码开发。将任务过程中填写的代码截图和每个步骤的输出结果粘贴在“竞赛任务应答书.docx”的指定位置中（如某一步骤无输出，应填“无”），其余过程文件按要求放置于work文件夹的指定位置。最后按照《选手指引》要求将“竞赛任务应答书.docx”存放于work文件夹，并导出压缩包，保存至指定位置。  
二、任务环境
硬件资源：高性能GPU计算机
软件资源：“竞赛资源.zip”，pytorch深度学习框架。
三、任务说明
本次任务中，使用python命令运行各py文件时，需要在对应文件所在目录打开终端，并使用正确环境（应该为module_A_env环境，在终端执行命令conda activate module_A_env即可切换）
任务中
1、人工智能职业基础
请打开竞赛资源task目录下的“人工智能职业基础.doc”，文档内有五道基础选择题，请将每题对应的选项填写在“竞赛任务应答书.docx”的相应位置。
2、命名实体提取任务（NER）
命名实体识别作为自然语言处理的基础任务，旨在从文本中识别并分类具有特定意义的实体，如人名、地名、机构名等，是信息抽取与语义理解的关键环节。文本中蕴含的实体信息往往是构建知识库的核心节点，通过精准识别这些实体，能够为文本内容建立结构化的语义锚点，帮助机器理解文本的深层含义。通过准确的命名实体识别，可以为医学研究、临床实践和医疗信息管理提供有价值的支持。对于医学研究者来说，能够从海量的医学文献中快速准确地提取出关键的实体信息，有助于深入研究疾病的发病机制、药物的疗效和安全性等问题。在临床实践中，医生可以利用命名实体识别技术从患者的病历、检查报告等文本中提取重要信息，辅助诊断和治疗决策。同时，命名实体识别也可以为医疗信息管理系统提供基础数据，实现对医学信息的高效组织和检索。
进入“task/NER”代码文件夹，进入到任务训练场景中。文件提供了所需的全部任务文件。其中“/data”为训练、测试所用的数据，*.py文件为模型训练评估的python代码文件，请根据相应文件中的已给内容编写代码完成以下操作：
任务中已给出标注好的命名实体数据，并划分为train、dev、和test数据集保存在/data文件夹内。先需要在convert2bioes.py文件中编写python代码，将JSON格式的数据转化为BIOES格式的标准命名实体标注数据，并将转化后的BIOES数据保存为train.bioes、dev.bioes、和test.bioes文件。具体格式如下：
字母	含义	举例
B	Begin，实体的开头	B-PER表示人名开头
I	Inside，实体的中间部分	I-PER表示人名中间
O	Outside，不属于任何实体	O就是背景词
E	End，实体的结尾	E-PER表示人名结尾
S	Single，单字实体	S-ORG表示一个词就是一个组织名
示例请参照train_sample.bioes文件。
（1）步骤1.转换文件格式（task/NER/convert2bioes.py）
部分代码已给出，请根据提示，将代码<1>处补充完整。
①定义split_sentences函数，该函数的任务是根据特定标点符号分割文本为句子列表。待分割的文本为string类型，分割后的句子保存到列表中。将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
（2）步骤2.根据JSON格式数据划分BIOES标注数据（task/NER/convert2bioes.py）
部分代码已给出，请根据提示，将代码<2>处补充完整。
①定义process_entity函数，根据entity类别属性，定义BIOES标签。分别对单字实体和多字实体进行处理。将<2>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
（3）步骤3.根据特定标点符号分割文本为句子列表（task/NER/convert2bioes.py）
部分代码已给出，请根据提示，将代码<3>处补充完整。
①定义convert_to_bioes函数，该函数的任务是将JSON格式的数据转换成BIOES标注格式。输入数据为包含段落和实体信息的字典结构，输出为BIOES格式的行列表。请根据JSON数据格式，提取文本中的段落、句子、实体。并使用（2）中的函数生成BOIES行列表。将<3>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
（4）步骤4.完成格式转变（task/NER/convert2bioes.py）
部分代码已给出，请根据提示，将代码补充完整。
①处理目录中的所有JSON文件，转换为BIOES格式。Input_dir为输入目录路径，所有文件的BIOES格式行列表。请补充<4>处代码，应使用（3）中定义的函数遍历目录中的所有JSON文件生成最终BOIES行列表。将<4>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
在终端通过python命令运行convert2bioes.py函数，将命令行输出截图保存到“竞赛任务应答书.docx”中指定位置，并将上述步骤的输出结果文件（train.bioes、test.bioes、dev.bioes）拷贝一份保存在“work”文件夹的“data”目录下。
（5）步骤5.构建模型输入数据(task/NER/data.py)
部分代码已提供，请根据以下说明，将 build_corpus 函数中的关键部分补充完整。该函数用于读取 NER 任务中以 BIOES 格式标注的数据文件（.bioes），并构建模型所需的输入数据结构。
①补全<1>处的代码，这里需要正确分割解析每一行的词和标签，从而将当前句子的词和标签分别添加到word_list和tag_list中。当遇到空行且当前列表非空时，还需要将当前列表分别添加到word_lists和tag_lists中，并重置列表。将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
②当参数make_vocab=True时，需使用文件中定义的build_map函数实现构建两个映射字典：
word2id：将每个唯一词语映射为一个唯一整数ID；
tag2id：将每个唯一标签映射为一个唯一整数 ID。
请在<2>处补充代码，并将<2>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
（6）步骤6.基于条件随机场（CRF）的序列标注模型(task/NER/models/crf.py)
部分代码已给出，请根据提示，将代码补充完整。
①编写训练函数，使用util.py中适当的函数提取语句特征，并将特征向量和标签列表作为输入训练模型。将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
②编写测试函数，使用util.py中适当的函数提取语句特征，输出模型预测结果。将<2>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
（7）步骤7.基于双向LSTM（BiLSTM）的序列标注模型(task/NER/models/bilstm.py)
部分代码已给出，请根据提示，将代码补充完整。
①根据词典大小和词向量维度，定义词向量。将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
②定义BiLSTM模型，补全输入维度、隐藏层维度、batch_first（True）、和双向LSTM的设置。将<2>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
③定义输出全连接层，补全输入维度（对应BiLSTM层的hidden_states）、输出维度的设置。将<3>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
④编写BiLSTM模型的forward函数。将输入的索引转换为词向量，输入BiLSTM进行编码，然后通过全连接层将输出映射到标签空间，得到每个位置的标签分数。将<4>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
（8）步骤8.基于隐马尔可夫模型（HMM）的序列标注模型(task/NER/models/hmm.py)
部分代码已给出，请根据提示，将代码补充完整。
①状态转移矩阵表示在HMM中从一个隐状态跳转到另一个隐状态的概率。如果当前处于状态，那么转移到状态的概率是。根据上述提示完成转移概率矩阵的估计，并对状态转移矩阵的每一行做归一化，使每一行的元素加起来为1。将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
②观测概率矩阵是HMM中用来表示每个隐藏状态生成各个观测值的概率分布，它描述了“某状态下出现某词语”的可能性，即在状态下生成观测符号的概率为。根据该提示完成观测概率矩阵的估计，并对观测概率矩阵的每一行做归一化，使每一行的元素加起来为 1。将<2>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
③估计HMM模型中的初始状态概率分布，表示每个隐藏状态作为序列第一个标签出现的概率。根据该原理完成初始状态概率分布的估计，并对初始状态概率分布做全局归一化，使得所有的元素加起来为 1。将<3>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
④根据维特比（Viterbi）算法递推公式补全代码。将<4>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
⑤从最后一个时间步所有状态中，找到概率最大的那一个状态。该状态对应的是整个最优路径的终点状态，它的概率就是这条路径的总最大概率。根据该提示补全代码。将<5>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
⑥从最优路径的终点（最后一个时间步）开始，利用 backpointer 数组逐步追溯每个状态的前驱状态，直到回到起点（第一个时间步）。通过这种方式，得到从起点到终点的完整最优状态路径，并根据回溯顺序拼接这些状态，最终形成最优路径。根据该提示补全代码。将<6>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
（9）步骤9.基于双向LSTM和条件随机场（BiLSTM-CRF）的序列标注模型(task/NER/models/bilstm_crf.py)
部分代码已给出，请根据提示，将代码补充完整。
①定义模型的transition转移矩阵，大小为（out_size, out_size），并进行均匀初始化。根据上述要求补全代码。将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
②通过BiLSTM提取每个字在每个标签下的发射分数，得到发射emission分数。将发射分数与标签间的转移分数相加，获得从标签𝑖转到标签𝑗的总打分。根据上述提示补全代码。将<2>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
③Viterbi解码的前向递推阶段，即根据打分一步步推导出每个时间步每个标签的最优路径分数和前驱标签。递推公式为。根据该提示补全代码。将<3>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
④补全BiLSTM-CRF的模型定义。将<4>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
⑤补全优化器的定义。优化器采用pytorch optim中的Adam。将<5>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
⑥补全代码完成模型的前向传播和损失计算。将<6>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
（10）步骤10.模型训练和测试（task/NER/evaluate.py）
部分代码已给出，请根据提示，将代码补充完整。
①补全CRF模型的调用和训练代码，训练后模型应该储存在“/ckpts”目录下，文件名为“crf.pkl”。补全模型的测试和性能指标评估代码。根据上述要求补全代码。将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
②补全HMM模型的调用和训练代码，训练后模型应该储存在“/ckpts”目录下，文件名为“hmm.pkl”。补全模型的测试和性能指标评估代码。根据上述要求补全代码。将<2>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
③补全BiLSTM模型的调用和训练代码（是否使用CRF作为模型输入参数），训练后模型应该储存在“/ckpts”目录下，文件名根据参数可能为“bilstm.pkl”或“bilstm_crf.pkl”。补全模型的测试和性能指标评估代码。根据上述要求补全代码。将<3>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
注：编写完成后，在终端通过python命令运行main.py进行模型训练和测试全流程（如果有未完成的模型，可注释main.py的对应模块完成运行）。最后将“./ckpts”中的模型保存到“work”文件夹的“model”目录中，将main.py文件运行结果“main_result.txt”保存到“work”文件夹的“data”目录中，将main.py文件保存到“work”文件夹的“code”目录中。
3、三元组提取（“KGC”）任务
三元组抽取是自然语言处理的核心任务，旨在从非结构化文本中提取（主体-关系-客体）结构化数据，支撑知识图谱构建。在医疗领域，可快速提取疾病-症状-药物关系，辅助临床决策和医学研究。
点击“KGC”代码文件夹，进入到任务训练场景中。文件提供了所需的全部任务文件。其中“/data”为训练、测试所用的数据，*.py文件为模型训练评估的python代码文件，请根据这相应文件中的已给内容编写代码完成以下操作：
数据处理
任务中已给出标注好的数据（CMeIE-V2_test_triples.jsonl），需要先在raw_data_process.py文件中编写python代码，将JSON格式的数据转化为TXT格式，并将转化后的TXT数据保存为source.txt和target.txt文件，保存在/data文件夹。源数据文件格式如下：
​​	文本字段（text）：存储原始医疗文本描述，内容包含疾病分类、病因、关联疾病等医学知识。
​​	三元组列表（triple_list）：以列表形式存储结构化三元组，每个三元组为 [subject, predicate, object] 结构：
​​Subject：医疗实体（如疾病、药物、症状）。
​​Predicate：实体间关系类型（如“病理分型”“病因”）。
​​Object：与主语关联的实体或属性值。
目标数据文件格式如下，示例请参照source_sample.txt和target_sample.txt文件：
source_sample.txt：存储原始文本内容，包含待提取的语义关系信息。
target_sample.txt：以列表形式存储结构化三元组。
source_sample.txt和target_sample.txt两个文件一一对应。
（1）步骤1.转换文件格式(task/KGC/data/raw_data_process.py)
部分代码已给出，请根据提示，将代码补充完整。
①定义jsonl_to_txt函数，该函数的任务是根据要求将jsonl文件转换为txt格式，并符合数据的格式要求。该文件能够转化后的数据保存为source.txt和target.txt文件，保存在“/KGC/data”目录下。将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。在终端通过python命令运行raw_data_process.py，将输出结果截图保存在“竞赛任务应答书.docx”中指定位置，将转化后的两个txt文件拷贝保存在“work”文件夹的data目录下。
（2）步骤2.配置文档路径参数(task/KGC/data/run_llm.py)
部分代码已给出，请根据提示，将代码补充完整。
①按提示配置三元组提取的源文件路径，并且将结果文件命名为result_kg.txt保存在“/KGC/data”目录下。将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
（3）步骤3.构造few shot示例(task/KGC/data/run_llm.py)
部分代码已给出，请根据提示，将代码补充完整。
①完善few shot示例，帮助大模型理解具体任务。部分代码已给出，请根据提示，将代码补充完整。将<2>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
提示：可选取CMeIE-V2_train_triples.jsonl中的例子作为few shot示例。
（4）步骤4.构造prompt提示词(task/KGC/data/run_llm.py)
部分代码已给出，请根据提示，将代码补充完整。
①构造三元组提取prompt提示词,程序会自动拼接prompt和few shot（作为一个示例）。将<3>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。然后通过python命令执行run_llm.py，利用大模型根据输入文本进行三元提取，并将结果保存到指定的文件result_kg.txt中。	
将①补充的代码截图保存在“竞赛任务应答书.docx”中指定位置，并将“./data”中的“result_kg.txt”文件，拷贝至work文件夹的data目录下。
（5）步骤5. 衡量提取精度(task/KGC/data/evaluation_script.py)
部分代码已给出，请根据提示，将代码补充完整。
简写	全称	含义
TP	True Positive	预测的三元组与真实三元组匹配
FP	False Positive	预测的三元组与真实三元组不匹配
FN	False Negative	未预测到的真实三元组
①补全大模型提取三元组和真实三元组之间的精度计算函数，这里的pred_set 和 truth_set 分别是预测结果和真实标签的集合，将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。在终端通过python命令执行evaluation_script.py文件，得到结果答案，将运行结果保存在“main_result.txt”。
编写完成后，需要将evaluation_script.py文件运行结果“main_result.txt”拷贝至work文件夹的data目录下。
知识图谱
知识图谱（Knowledge Graph, KG）是一种以图结构形式组织和表达知识的技术。它以实体（节点）和实体间的关系（边）为核心，能够有效地表示现实世界中的复杂语义关联。知识图谱广泛应用于智能问答、推荐系统、语义搜索等领域，能够帮助计算机更好地理解和推理人类知识。这里我们通过提取的三元组简单地构建一个知识图谱。
（6）步骤6. 知识图谱构建(KGC/data/knowledge_graph.py)
部分代码已给出，请根据提示，将代码补充完整。
①为知识图谱添加关系边（即在有向图中添加从 head 到 tail 的边，并设置边的属性 relation），将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
②补全获取所有入边邻居的相关代码，将<2>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
③通过NetworkX 最短路径查找的相关参数，将<3>处补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。
编写完成后，在终端通过python命令运行knowledge_graph.py文件，将终端运行测试结果截图保存到“竞赛任务应答书.docx”中指定位置。将生成的“knowledge_graph.json”文件拷贝到“work”文件夹的“data”目录下。