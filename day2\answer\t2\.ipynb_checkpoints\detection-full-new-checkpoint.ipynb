{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# import netron\n", "import torch\n", "from PIL import Image\n", "# import onnx\n", "import sys\n", "import os\n", "import numpy as np\n", "from pathlib import Path\n", "from typing import Union\n", "import cv2\n", "from ultralytics import YOLO"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def train():\n", "    # 加载模型配置文件，重头开始训练\n", "    # model = YOLO('yolov8s.yaml')\n", "\n", "    # 使用预训练模型进行训练\n", "    model = YOLO('weights/yolov8s.pt')\n", "    # model = YOLO('yolov8n.yaml').load('yolov8n.pt')\n", "\n", "    # 使用medicine数据集训练模型\n", "    model.train(data=\"medicine.yaml\", epochs=100, imgsz=640)\n", "\n", "def onnx():\n", "    # 使用onnx导出文件\n", "    # model = YOLO('yolov8n.pt')  # load an official model\n", "    model = YOLO('runs/detect/train/weights/best.pt')  # load a custom trained\n", "    # Export the model\n", "    model.export(format='onnx')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def test_img():\n", "\t# 训练好的模型权重路径\n", "    model = YOLO(\"runs/detect/train/weights/best.pt\")\n", "    # 测试图片的路径\n", "    img = cv2.imread(\"test.jpg\")\n", "    res = model(img)\n", "    ann = res[0].plot()\n", "    while True:\n", "        cv2.imshow(\"yolo\", ann)\n", "        if cv2.wait<PERSON><PERSON>(1) & 0xFF == ord('q'):\n", "            break\n", "    # 设置保存图片的路径\n", "    cur_path = sys.path[0]\n", "    print(cur_path, sys.path)\n", "\n", "    if os.path.exists(cur_path):\n", "        cv2.imwrite(cur_path + \"out.jpg\", ann)\n", "    else:\n", "        os.mkdir(cur_path)\n", "        cv2.imwrite(cur_path + \"out.jpg\", ann)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def predict():\n", "    # Load a model\n", "    # model = YOLO('yolov8n.pt')  # 加载官方的模型权重作评估\n", "    model = YOLO('runs/detect/train/weights/best.pt')  # 加载自定义的模型权重作评估\n", "\n", "    # 评估\n", "    metrics = model.val()\n", "    # 如果要在新的数据集上测试训练结果，需要将数据集绝对路径传入，例如：\n", "    # metrics = model.val(data=“YOLOv8/.../VOC.yaml”)\n", "    print(metrics.box.map)  # map50-95\n", "    print(metrics.box.map50)  # map50\n", "    print(metrics.box.map75)  # map75\n", "    print(metrics.box.maps)  # 包含每个类别的map50-95列表\n", "\n", "    # Accessing AP75 for each category\n", "    ap75_each_category = metrics.box.maps[:, 5]  # 利用maps矩阵可以得到AP75\n", "    print(ap75_each_category)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New https://pypi.org/project/ultralytics/8.3.176 available  Update with 'pip install -U ultralytics'\n", "Ultralytics 8.3.170  Python-3.8.20 torch-2.4.1+cpu CPU (AMD Ryzen 5 7500F 6-Core Processor)\n", "\u001b[34m\u001b[1mengine\\trainer: \u001b[0magnostic_nms=False, amp=True, augment=False, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=medicine.yaml, degrees=0.0, deterministic=True, device=cpu, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=100, erasing=0.4, exist_ok=False, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=640, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=weights/yolov8s.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=train19, nbs=64, nms=False, opset=None, optimize=False, optimizer=auto, overlap_mask=True, patience=100, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=None, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=runs\\detect\\train19, save_frames=False, save_json=False, save_period=-1, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3.0, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None\n", "Overriding model.yaml nc=80 with nc=1\n", "\n", "                   from  n    params  module                                       arguments                     \n", "  0                  -1  1       928  ultralytics.nn.modules.conv.Conv             [3, 32, 3, 2]                 \n", "  1                  -1  1     18560  ultralytics.nn.modules.conv.Conv             [32, 64, 3, 2]                \n", "  2                  -1  1     29056  ultralytics.nn.modules.block.C2f             [64, 64, 1, True]             \n", "  3                  -1  1     73984  ultralytics.nn.modules.conv.Conv             [64, 128, 3, 2]               \n", "  4                  -1  2    197632  ultralytics.nn.modules.block.C2f             [128, 128, 2, True]           \n", "  5                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]              \n", "  6                  -1  2    788480  ultralytics.nn.modules.block.C2f             [256, 256, 2, True]           \n", "  7                  -1  1   1180672  ultralytics.nn.modules.conv.Conv             [256, 512, 3, 2]              \n", "  8                  -1  1   1838080  ultralytics.nn.modules.block.C2f             [512, 512, 1, True]           \n", "  9                  -1  1    656896  ultralytics.nn.modules.block.SPPF            [512, 512, 5]                 \n", " 10                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 11             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 12                  -1  1    591360  ultralytics.nn.modules.block.C2f             [768, 256, 1]                 \n", " 13                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 14             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 15                  -1  1    148224  ultralytics.nn.modules.block.C2f             [384, 128, 1]                 \n", " 16                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]              \n", " 17            [-1, 12]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 18                  -1  1    493056  ultralytics.nn.modules.block.C2f             [384, 256, 1]                 \n", " 19                  -1  1    590336  ultralytics.nn.modules.conv.Conv             [256, 256, 3, 2]              \n", " 20             [-1, 9]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 21                  -1  1   1969152  ultralytics.nn.modules.block.C2f             [768, 512, 1]                 \n", " 22        [15, 18, 21]  1   2116435  ultralytics.nn.modules.head.Detect           [1, [128, 256, 512]]          \n", "Model summary: 129 layers, 11,135,987 parameters, 11,135,971 gradients, 28.6 GFLOPs\n", "\n", "Transferred 349/355 items from pretrained weights\n", "Freezing layer 'model.22.dfl.conv.weight'\n", "\u001b[34m\u001b[1mtrain: \u001b[0mFast image access  (ping: 0.10.0 ms, read: 242.288.9 MB/s, size: 32.4 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mScanning D:\\DoctorResearch\\work\\国赛\\题目\\task\\t2\\datasets\\medicine\\Images\\train.cache... 0 images, 1276 backgrounds, 0 corrupt: 100%|██████████| 1276/1276 [00:00<?, ?it/s]\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING Labels are missing or empty in D:\\DoctorResearch\\work\\\\\\task\\t2\\datasets\\medicine\\Images\\train.cache, training may not work correctly. See https://docs.ultralytics.com/datasets for dataset formatting guidance.\n", "\u001b[34m\u001b[1mval: \u001b[0mFast image access  (ping: 0.10.0 ms, read: 311.8138.3 MB/s, size: 29.9 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "\u001b[34m\u001b[1mval: \u001b[0mScanning D:\\DoctorResearch\\work\\国赛\\题目\\task\\t2\\datasets\\medicine\\Images\\valid.cache... 0 images, 365 backgrounds, 0 corrupt: 100%|██████████| 365/365 [00:00<?, ?it/s]\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING Labels are missing or empty in D:\\DoctorResearch\\work\\\\\\task\\t2\\datasets\\medicine\\Images\\valid.cache, training may not work correctly. See https://docs.ultralytics.com/datasets for dataset formatting guidance.\n", "Plotting labels to runs\\detect\\train19\\labels.jpg... \n", "WARNING zero-size array to reduction operation maximum which has no identity\n", "\u001b[34m\u001b[1moptimizer:\u001b[0m 'optimizer=auto' found, ignoring 'lr0=0.01' and 'momentum=0.937' and determining best 'optimizer', 'lr0' and 'momentum' automatically... \n", "\u001b[34m\u001b[1moptimizer:\u001b[0m AdamW(lr=0.002, momentum=0.9) with parameter groups 57 weight(decay=0.0), 64 weight(decay=0.0005), 63 bias(decay=0.0)\n", "Image sizes 640 train, 640 val\n", "Using 0 dataloader workers\n", "Logging results to \u001b[1mruns\\detect\\train19\u001b[0m\n", "Starting training for 100 epochs...\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "      1/100         0G          0        463          0          0        640:   9%|▉         | 7/80 [00:36<06:18,  5.19s/it]"]}], "source": ["train()\n", "test_img()\n", "predict()\n", "onnx()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python (yolov8)", "language": "python", "name": "yolov8"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 4}