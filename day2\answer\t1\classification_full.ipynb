{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import random\n", "import numpy as np\n", "import torch.optim as optim\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.parallel\n", "import torch.nn.functional as F\n", "import torch.optim\n", "import torch.utils.data\n", "import torch.utils.data.distributed\n", "import torchvision.transforms as transforms\n", "import torchvision.datasets as datasets\n", "import torchvision.models\n", "from torch.autograd import Variable\n", "import math"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 设置全局参数\n", "modellr = 1e-4\n", "BATCH_SIZE = 12\n", "EPOCHS = 2\n", "DEVICE = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def _get_pixels(per_pixel, rand_color, patch_size, dtype=torch.float32, device=DEVICE):\n", "   if per_pixel:\n", "       return torch.empty(patch_size, dtype=dtype, device=device).normal_()\n", "   elif rand_color:\n", "       return torch.empty((patch_size[0], 1, 1), dtype=dtype, device=device).normal_()\n", "   else:\n", "       return torch.zeros((patch_size[0], 1, 1), dtype=dtype, device=device)\n", "    \n", "class RandomErasing:\n", "   def __init__(self, probability=0.5, min_area=0.02, max_area=1/3, min_aspect=0.3, max_aspect=None, mode='const', min_count=1, max_count=None, num_splits=0, device=DEVICE):\n", "       self.probability = probability\n", "       self.min_area = min_area\n", "       self.max_area = max_area\n", "       max_aspect = max_aspect or 1 / min_aspect\n", "       self.log_aspect_ratio = (math.log(min_aspect), math.log(max_aspect))\n", "       self.min_count = min_count\n", "       self.max_count = max_count or min_count\n", "       self.num_splits = num_splits\n", "       self.mode = mode.lower()\n", "       self.rand_color = False\n", "       self.per_pixel = False\n", "       if self.mode == 'rand':\n", "           self.rand_color = True\n", "       elif self.mode == 'pixel':\n", "           self.per_pixel = True\n", "       self.device = device\n", "    \n", "   def _erase(self, img, chan, img_h, img_w, dtype):\n", "       if random.random() > self.probability:\n", "           return\n", "       area = img_h * img_w\n", "       count = self.min_count if self.min_count == self.max_count else random.randint(self.min_count, self.max_count)\n", "       for _ in range(count):\n", "           for attempt in range(10):\n", "               target_area = random.uniform(self.min_area, self.max_area) * area / count\n", "               aspect_ratio = math.exp(random.uniform(*self.log_aspect_ratio))\n", "               h = int(round(math.sqrt(target_area * aspect_ratio)))\n", "               w = int(round(math.sqrt(target_area / aspect_ratio)))\n", "               if w < img_w and h < img_h:\n", "                   top = random.randint(0, img_h - h)\n", "                   left = random.randint(0, img_w - w)\n", "                   img[:, top:top + h, left:left + w] = _get_pixels(self.per_pixel, self.rand_color, (chan, h, w), dtype=dtype, device=self.device)\n", "                   break\n", "                    \n", "   def __call__(self, input):\n", "       if len(input.size()) == 3:\n", "           self._erase(input, *input.size(), input.dtype)\n", "       else:\n", "           batch_size, chan, img_h, img_w = input.size()\n", "           batch_start = batch_size // self.num_splits if self.num_splits > 1 else 0\n", "           for i in range(batch_start, batch_size):\n", "               self._erase(input[i], chan, img_h, img_w, input.dtype)\n", "       return input\n", "\n", "   def __repr__(self):\n", "       fs = self.__class__.__name__ + f'(p={self.probability}, mode={self.mode}'\n", "       fs += f', count=({self.min_count}, {self.max_count}))'\n", "       return fs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["transform:  Compose(\n", "    Resize(size=(224, 224), interpolation=bilinear, max_size=None, antialias=None)\n", "    ToTensor()\n", "    Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])\n", "    RandomErasing(p=0.5, mode=const, count=(1, 1))\n", ")\n", "transform_test:  Compose(\n", "    Resize(size=(224, 224), interpolation=bilinear, max_size=None, antialias=None)\n", "    ToTensor()\n", "    Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])\n", ")\n"]}], "source": ["# 数据预处理操作定义\n", "transform = transforms.Compose([\n", "    transforms.Resize((224, 224)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5]),\n", "    RandomErasing()\n", "])\n", "transform_test = transforms.Compose([\n", "    transforms.Resize((224, 224)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])\n", "])\n", "\n", "print(\"transform: \", transform)\n", "print(\"transform_test: \", transform_test)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["transform_a:  Compose(\n", "    RandomHorizontalFlip(p=0.8)\n", "    RandomRotation(degrees=[-30.0, 30.0], interpolation=nearest, expand=False, fill=0)\n", "    CenterCrop(size=(144, 144))\n", "    Resize(size=(224, 224), interpolation=bilinear, max_size=None, antialias=None)\n", "    ToTensor()\n", "    Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])\n", "    RandomErasing(p=0.5, mode=const, count=(1, 1))\n", ")\n"]}], "source": ["# 数据增强操作定义\n", "transform_a = transforms.Compose([\n", "    transforms.RandomHorizontalFlip(p=0.8),\n", "    transforms.RandomRotation(degrees=30,expand=False),\n", "    transforms.CenterCrop(144),\n", "    transforms.Resize((224, 224)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5]),\n", "    RandomErasing()\n", "])\n", "\n", "print(\"transform_a: \", transform_a)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["扩充前训练集样本数量:  5232\n", "训练集类别标签:  {'NORMAL': 0, 'PNEUMONIA': 1}\n", "扩充后训练集样本数量:  10464\n", "测试集类别标签:  {'NORMAL': 0, 'PNEUMONIA': 1}\n"]}], "source": ["# 读取数据并同时进行预处理\n", "# 读取训练集数据\n", "dataset_train = datasets.ImageFolder('./data/train', transform)\n", "print(\"扩充前训练集样本数量: \", len(dataset_train.imgs))\n", "# 读取测试集数据\n", "dataset_test = datasets.ImageFolder('./data/test', transform_test)\n", "\n", "# 使用数据增广操作“transform_a”扩充训练集\n", "# 对训练集进行增强\n", "dataset_train_a = datasets.ImageFolder('./data/train', transform_a)\n", "# 调用接口扩充训练集数据\n", "dataset_train.class_to_idx.update(dataset_train_a.class_to_idx)\n", "dataset_train.imgs.extend(dataset_train_a.imgs)\n", "\n", "print(\"训练集类别标签: \", dataset_train.class_to_idx)\n", "print(\"扩充后训练集样本数量: \", len(dataset_train.imgs))\n", "print(\"测试集类别标签: \", dataset_test.class_to_idx)\n", " \n", "# 创建训练集和测试集的Dataloader对象\n", "train_loader = torch.utils.data.DataLoader(dataset_train, batch_size=BATCH_SIZE, shuffle=True)\n", "test_loader = torch.utils.data.DataLoader(dataset_test, batch_size=BATCH_SIZE, shuffle=False)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["class CrossEntropyLoss(nn.Module):\n", "    def __init__(self) -> None:\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "    def forward(self, pred, target):\n", "        pred = torch.softmax(pred, dim=-1)\n", "        one_hot = F.one_hot(target).float()\n", "        log = torch.log(pred)\n", "        loss = -torch.sum(one_hot * log)\n", "    \n", "        return loss"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda/envs/module_B_env/lib/python3.8/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n", "  warnings.warn(\n", "/home/<USER>/miniconda/envs/module_B_env/lib/python3.8/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=None`.\n", "  warnings.warn(msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["修改后模型的全连接层为:  Linear(in_features=512, out_features=2, bias=True)\n", "使用的损失函数为:  CrossEntropyLoss()\n", "使用的优化器为:  Adam (\n", "Parameter Group 0\n", "    amsgrad: False\n", "    betas: (0.9, 0.999)\n", "    capturable: False\n", "    differentiable: False\n", "    eps: 1e-08\n", "    foreach: None\n", "    fused: False\n", "    lr: 0.0001\n", "    maximize: False\n", "    weight_decay: 0\n", ")\n"]}], "source": ["# 构建模型，并将其移动到和数据相同的设备上\n", "model = torchvision.models.resnet18(pretrained=False)\n", "in_feats = model.fc.in_features\n", "# 按类别数设置模型全连接层的输出维度\n", "model.fc = nn.Linear(in_feats, 2)\n", "model.to(DEVICE)\n", "# 选择交叉熵损失\n", "criterion = CrossEntropyLoss()\n", "# 选择Adam优化器\n", "optimizer = optim.Adam(model.parameters(), lr=modellr)\n", "\n", "print(\"修改后模型的全连接层为: \", model.fc)\n", "print(\"使用的损失函数为: \", criterion)\n", "print(\"使用的优化器为: \", optimizer)\n", " "]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "# 定义训练过程\n", "def train(model, device, train_loader, optimizer, epoch):\n", "    model.train()\n", "    sum_loss = 0\n", "    total_num = len(train_loader.dataset)\n", "    # 打印总训练样本数和总iteration数\n", "    print(f'总训练样本数: {total_num}, 总iteration数: {len(train_loader)}')\n", "    for batch_idx, (data, target) in tqdm(enumerate(train_loader)):\n", "        data, target = Variable(data).to(device), Variable(target).to(device)\n", "        output = model(data)\n", "        # 计算当前iteration的损失函数\n", "        loss = criterion(output, target)\n", "        optimizer.zero_grad()\n", "        # 进行梯度反传\n", "        loss.backward()\n", "        optimizer.step()\n", "        print_loss = loss.data.item()\n", "        sum_loss += print_loss\n", "        # 每50个iteration打印当前的训练进度和损失函数\n", "        if (batch_idx + 1) % 200 == 0:\n", "            print('Train Epoch: {} [{}/{} ({:.0f}%)]\\tLoss: {:.6f}'.format(\n", "                epoch, (batch_idx + 1) * len(data), len(train_loader.dataset),\n", "                       100. * (batch_idx + 1) / len(train_loader), loss.item()))\n", "    # 计算该epoch的平均loss\n", "    ave_loss = sum_loss / len(train_loader)\n", "    print('epoch:{},loss:{}'.format(epoch, ave_loss))\n", " \n", "# 定义验证过程\n", "def val(model, device, test_loader):\n", "    # 将模型设置到预测模式\n", "    model.eval()\n", "    test_loss = 0\n", "    correct = 0\n", "    total_num = len(test_loader.dataset)\n", "    # 打印总测试样本数和总iteration数\n", "    print(f'总测试样本数: {total_num}, 总iteration数: {len(test_loader)}')\n", "    with torch.no_grad():\n", "        for data, target in tqdm(test_loader):\n", "            data, target = Variable(data).to(device), Variable(target).to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "            _, pred = torch.max(output.data, 1)\n", "            # 判断预测结果是否准确，若准确则增加准确预测数\n", "            correct += torch.sum(pred == target)\n", "            print_loss = loss.data.item()\n", "            test_loss += print_loss\n", "        correct = correct.data.item()\n", "        # 计算模型准确率\n", "        acc = correct / total_num\n", "        avgloss = test_loss / len(test_loader)\n", "        print('\\nVal set: Average loss: {:.4f}, Accuracy: {}/{} ({:.0f}%)\\n'.format(\n", "            avgloss, correct, len(test_loader.dataset), 100 * acc))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["总训练样本数: 10464, 总iteration数: 872\n"]}, {"name": "stderr", "output_type": "stream", "text": ["200it [01:49,  2.04it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Train Epoch: 1 [2400/10464 (23%)]\tLoss: 0.626979\n"]}, {"name": "stderr", "output_type": "stream", "text": ["400it [03:37,  2.02it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Train Epoch: 1 [4800/10464 (46%)]\tLoss: 2.809119\n"]}, {"name": "stderr", "output_type": "stream", "text": ["600it [05:24,  1.85it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Train Epoch: 1 [7200/10464 (69%)]\tLoss: 0.570730\n"]}, {"name": "stderr", "output_type": "stream", "text": ["800it [07:12,  1.69it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Train Epoch: 1 [9600/10464 (92%)]\tLoss: 0.165106\n"]}, {"name": "stderr", "output_type": "stream", "text": ["872it [07:49,  1.86it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch:1,loss:1.7676429082233664\n", "总测试样本数: 624, 总iteration数: 52\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 52/52 [00:31<00:00,  1.65it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Val set: Average loss: 15.0589, Accuracy: 544/624 (87%)\n", "\n", "总训练样本数: 10464, 总iteration数: 872\n"]}, {"name": "stderr", "output_type": "stream", "text": ["200it [01:49,  1.75it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Train Epoch: 2 [2400/10464 (23%)]\tLoss: 0.122961\n"]}, {"name": "stderr", "output_type": "stream", "text": ["400it [03:40,  1.68it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Train Epoch: 2 [4800/10464 (46%)]\tLoss: 0.043812\n"]}, {"name": "stderr", "output_type": "stream", "text": ["600it [05:29,  1.82it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Train Epoch: 2 [7200/10464 (69%)]\tLoss: 1.446973\n"]}, {"name": "stderr", "output_type": "stream", "text": ["800it [07:14,  2.65it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Train Epoch: 2 [9600/10464 (92%)]\tLoss: 0.239672\n"]}, {"name": "stderr", "output_type": "stream", "text": ["872it [07:52,  1.84it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch:2,loss:0.8344630949052879\n", "总测试样本数: 624, 总iteration数: 52\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 52/52 [00:31<00:00,  1.63it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Val set: Average loss: 21.2785, Accuracy: 444/624 (71%)\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# 训练\n", "for epoch in range(1, EPOCHS + 1):\n", "    train(model, DEVICE, train_loader, optimizer, epoch)\n", "    val(model, DEVICE, test_loader)\n", "# 保存模型\n", "torch.save(model, 'model_resnet18.pth')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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**************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 测试结果及可视化\n", "import torch.utils.data.distributed\n", "import torchvision.transforms as transforms\n", " \n", "from torch.autograd import Variable\n", "import os\n", "from PIL import Image\n", "import matplotlib.pyplot as plt\n", "import cv2\n", "\n", "def gray_to_rgb(gray_data):\n", "    rgb_data = np.zeros((3, gray_data.shape[1],gray_data.shape[2]),dtype=np.uint8)\n", "    rgb_data[0, ...]= gray_data\n", "    rgb_data[1, ...]= gray_data\n", "    rgb_data[2, ...]= gray_data\n", "    return torch.tensor(rgb_data, dtype=torch.float32)\n", " \n", "classes = ('NORMAL', 'PNEUMONIA')\n", "# 同训练一样进行数据预处理\n", "transform_test_ = transforms.Compose([\n", "    transforms.Resize((224, 224)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize([0.5], [0.5])\n", "])\n", "# 读取保存的模型\n", "model = torch.load(\"model_resnet18.pth\")\n", "model.eval()\n", "model.to(DEVICE)\n", "path='data/vis/'\n", "# 根据path读取测试样本列表\n", "testList=os.listdir(path)[:1]\n", "\n", "# 分类样本并可视化\n", "fig = plt.figure()\n", "idx= 0\n", "for file in testList:\n", "    img_ori = Image.open(path+file)\n", "    img = transform_test_(img_ori)\n", "    img = gray_to_rgb(img)\n", "    img_ = torch.unsqueeze(img, dim=0)\n", "    img_ = Variable(img_).to(DEVICE)\n", "    out = model(img_)\n", "    # 预测分类\n", "    _, pred = torch.max(out.data, 1)\n", "    # 设置包含子图的图像格式\n", "    plt.subplot(1, 4, idx+1)\n", "    #自动调整子图参数，使之填充满整个图像区域\n", "    plt.tight_layout()\n", "    # 展示图像及其预测类别，类别作为子图标题\n", "    plt.imshow(img_ori, interpolation='none')\n", "    plt.title(\"{}\".format(classes[pred.data.item()]))\n", "    plt.axis('off')\n", "    idx += 1\n", " \n", "# 展示图象\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# 获取全连接层的输入\n", "class Identity(nn.Mo<PERSON><PERSON>):\n", "    def __init__(self):\n", "        super(Identity, self).__init__()\n", "\n", "    def forward(self, x):\n", "        return x"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "\n", "classes = ['NORMAL', 'PNEUMONIA']\n", "# 读取用于可视化的测试集2\n", "path='data/test/'\n", "\n", "dataset_test_ = datasets.ImageFolder(path, transform_test)\n", "test_loader_ = torch.utils.data.DataLoader(dataset_test_, batch_size=BATCH_SIZE, shuffle=False)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1,\n", "        1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1,\n", "        1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0,\n", "        1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 1, 0, 0,\n", "        0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1,\n", "        0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],\n", "       device='cuda:0')\n"]}], "source": ["model_cls = torch.load(\"model_resnet18.pth\")\n", "model_cls.eval()\n", "model_cls.to(DEVICE)\n", "\n", "cls = []\n", "with torch.no_grad():\n", "    for data, target in test_loader_:\n", "        data, target = Variable(data).to(DEVICE), Variable(target).to(DEVICE)\n", "        output = model_cls(data)\n", "        cls.append(output)\n", "cls = torch.cat(cls)\n", "_, cls_pred = torch.max(cls, 1)\n", "print(cls_pred)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([624, 512])\n"]}], "source": ["model_feat = torch.load(\"model_resnet18.pth\")\n", "model_cls.fc = Identity()\n", "model_feat.eval()\n", "model_feat.to(DEVICE)\n", "\n", "feats = []\n", "with torch.no_grad():\n", "    for data, target in test_loader_:\n", "        data, target = Variable(data).to(DEVICE), Variable(target).to(DEVICE)\n", "        output = model_cls(data)\n", "        feats.append(output)\n", "feats = torch.cat(feats)\n", "print(feats.shape)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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***************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.manifold import TSNE\n", "import seaborn as sns\n", "\n", "feats = feats.cpu().numpy()\n", "\n", "tsne = TSNE(n_components=2, init='pca', random_state=0)\n", "# 预测结果可视化\n", "test_feats_emb = tsne.fit_transform(feats)\n", "labels = cls_pred\n", "labels = labels.tolist()\n", "str_labels = []\n", "for label in labels:\n", "    if label == 0:\n", "        label = 'NORMAL'\n", "    else:\n", "        label = 'PNEUMONIA'\n", "    str_labels.append(label)\n", "sns.scatterplot(x=test_feats_emb[:,0], y=test_feats_emb[:,1], hue=str_labels)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "module_B_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 5}