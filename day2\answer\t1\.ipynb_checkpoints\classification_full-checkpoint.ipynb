{"cells": [{"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["import os\n", "import random\n", "import numpy as np\n", "import torch.optim as optim\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.parallel\n", "import torch.nn.functional as F\n", "import torch.optim\n", "import torch.utils.data\n", "import torch.utils.data.distributed\n", "import torchvision.transforms as transforms\n", "import torchvision.datasets as datasets\n", "import torchvision.models\n", "from torch.autograd import Variable\n", "import math"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["# 设置全局参数\n", "modellr = 1e-4\n", "BATCH_SIZE = 12\n", "EPOCHS = 10\n", "DEVICE = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["def _get_pixels(per_pixel, rand_color, patch_size, dtype=torch.float32, device=DEVICE):\n", "   if per_pixel:\n", "       return torch.empty(patch_size, dtype=dtype, device=device).normal_()\n", "   elif rand_color:\n", "       return torch.empty((patch_size[0], 1, 1), dtype=dtype, device=device).normal_()\n", "   else:\n", "       return torch.zeros((patch_size[0], 1, 1), dtype=dtype, device=device)\n", "    \n", "class RandomErasing:\n", "   def __init__(self, probability=0.5, min_area=0.02, max_area=1/3, min_aspect=0.3, max_aspect=None, mode='const', min_count=1, max_count=None, num_splits=0, device=DEVICE):\n", "       self.probability = probability\n", "       self.min_area = min_area\n", "       self.max_area = max_area\n", "       max_aspect = max_aspect or 1 / min_aspect\n", "       self.log_aspect_ratio = (math.log(min_aspect), math.log(max_aspect))\n", "       self.min_count = min_count\n", "       self.max_count = max_count or min_count\n", "       self.num_splits = num_splits\n", "       self.mode = mode.lower()\n", "       self.rand_color = False\n", "       self.per_pixel = False\n", "       if self.mode == 'rand':\n", "           self.rand_color = True\n", "       elif self.mode == 'pixel':\n", "           self.per_pixel = True\n", "       self.device = device\n", "    \n", "   def _erase(self, img, chan, img_h, img_w, dtype):\n", "       if random.random() > self.probability:\n", "           return\n", "       area = img_h * img_w\n", "       count = self.min_count if self.min_count == self.max_count else random.randint(self.min_count, self.max_count)\n", "       for _ in range(count):\n", "           for attempt in range(10):\n", "               target_area = random.uniform(self.min_area, self.max_area) * area / count\n", "               aspect_ratio = math.exp(random.uniform(*self.log_aspect_ratio))\n", "               h = int(round(math.sqrt(target_area * aspect_ratio)))\n", "               w = int(round(math.sqrt(target_area / aspect_ratio)))\n", "               if w < img_w and h < img_h:\n", "                   top = random.randint(0, img_h - h)\n", "                   left = random.randint(0, img_w - w)\n", "                   img[:, top:top + h, left:left + w] = _get_pixels(self.per_pixel, self.rand_color, (chan, h, w), dtype=dtype, device=self.device)\n", "                   break\n", "                    \n", "   def __call__(self, input):\n", "       if len(input.size()) == 3:\n", "           self._erase(input, *input.size(), input.dtype)\n", "       else:\n", "           batch_size, chan, img_h, img_w = input.size()\n", "           batch_start = batch_size // self.num_splits if self.num_splits > 1 else 0\n", "           for i in range(batch_start, batch_size):\n", "               self._erase(input[i], chan, img_h, img_w, input.dtype)\n", "       return input\n", "\n", "   def __repr__(self):\n", "       fs = self.__class__.__name__ + f'(p={self.probability}, mode={self.mode}'\n", "       fs += f', count=({self.min_count}, {self.max_count}))'\n", "       return fs"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["transform:  Compose(\n", "    Resize(size=(224, 224), interpolation=bilinear, max_size=None, antialias=True)\n", "    ToTensor()\n", "    Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])\n", "    RandomErasing(p=0.5, mode=const, count=(1, 1))\n", ")\n", "transform_test:  Compose(\n", "    Resize(size=(224, 224), interpolation=bilinear, max_size=None, antialias=True)\n", "    ToTensor()\n", "    Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])\n", "    RandomErasing(p=0.5, mode=const, count=(1, 1))\n", ")\n"]}], "source": ["# 数据预处理操作定义\n", "transform = transforms.Compose([\n", "    transforms.Resize((224, 224)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5]),\n", "    RandomErasing()\n", "])\n", "transform_test = transforms.Compose([\n", "    transforms.Resize((224, 224)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5]),\n", "    RandomErasing()\n", "])\n", "\n", "print(\"transform: \", transform)\n", "print(\"transform_test: \", transform_test)"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["transform_a:  Compose(\n", "    RandomHorizontalFlip(p=0.8)\n", "    RandomRotation(degrees=[-30.0, 30.0], interpolation=nearest, expand=False, fill=0)\n", "    CenterCrop(size=(144, 144))\n", "    Resize(size=(224, 224), interpolation=bilinear, max_size=None, antialias=True)\n", "    ToTensor()\n", "    Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])\n", "    RandomErasing(p=0.5, mode=const, count=(1, 1))\n", ")\n"]}], "source": ["# 数据增强操作定义\n", "transform_a = transforms.Compose([\n", "    transforms.RandomHorizontalFlip(p=0.8),\n", "    transforms.RandomRotation(degrees=30,expand=False),\n", "    transforms.CenterCrop(144),\n", "    transforms.Resize((224, 224)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5]),\n", "    RandomErasing()\n", "])\n", "\n", "print(\"transform_a: \", transform_a)"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["扩充前训练集样本数量:  5232\n", "训练集类别标签:  {'NORMAL': 0, 'PNEUMONIA': 1}\n", "扩充后训练集样本数量:  10464\n", "测试集类别标签:  {'NORMAL': 0, 'PNEUMONIA': 1}\n"]}], "source": ["# 读取数据并同时进行预处理\n", "# 读取训练集数据\n", "dataset_train = datasets.ImageFolder('./data/train', transform)\n", "print(\"扩充前训练集样本数量: \", len(dataset_train.imgs))\n", "# 读取测试集数据\n", "dataset_test = datasets.ImageFolder('./data/test', transform_test)\n", "# dataset_val = datasets.ImageFolder('./data/val', transform_test)\n", "\n", "# 使用数据增广操作“transform_a”扩充训练集\n", "# 对训练集进行增强\n", "dataset_train_a = datasets.ImageFolder('./data/train', transform_a)\n", "# 调用接口扩充训练集数据\n", "# dataset_train.classes.extend(dataset_train_a.classes)\n", "dataset_train.class_to_idx.update(dataset_train_a.class_to_idx)\n", "dataset_train.imgs.extend(dataset_train_a.imgs)\n", "\n", "print(\"训练集类别标签: \", dataset_train.class_to_idx)\n", "print(\"扩充后训练集样本数量: \", len(dataset_train.imgs))\n", "print(\"测试集类别标签: \", dataset_test.class_to_idx)\n", "# torch.save(dataset_train, 'data_train.pt')\n", " \n", "# 创建训练集和测试集的Dataloader对象\n", "train_loader = torch.utils.data.DataLoader(dataset_train, batch_size=BATCH_SIZE, shuffle=True)\n", "test_loader = torch.utils.data.DataLoader(dataset_test, batch_size=BATCH_SIZE, shuffle=False)"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["class CrossEntropyLoss(nn.Module):\n", "    def __init__(self) -> None:\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "    def forward(self, pred, target):\n", "        pred = torch.softmax(pred, dim=-1)\n", "        one_hot = F.one_hot(target).float()\n", "        log = torch.log(pred)\n", "        loss = -torch.sum(one_hot * log) / target.shape[0]\n", "    \n", "        return loss"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["修改后模型的全连接层为:  Linear(in_features=512, out_features=2, bias=True)\n", "使用的损失函数为:  CrossEntropyLoss()\n", "使用的优化器为:  Adam (\n", "Parameter Group 0\n", "    amsgrad: False\n", "    betas: (0.9, 0.999)\n", "    capturable: False\n", "    differentiable: False\n", "    eps: 1e-08\n", "    foreach: None\n", "    fused: None\n", "    lr: 0.0001\n", "    maximize: False\n", "    weight_decay: 0\n", ")\n"]}], "source": ["# 构建模型，并将其移动到和数据相同的设备上\n", "model = torchvision.models.resnet18(pretrained=False)\n", "in_feats = model.fc.in_features\n", "# 按类别数设置模型全连接层的输出维度\n", "model.fc = nn.Linear(in_feats, 2)\n", "model.to(DEVICE)\n", "# 选择交叉熵损失\n", "criterion = CrossEntropyLoss()\n", "\n", "# 选择Adam优化器\n", "optimizer = optim.Adam(model.parameters(), lr=modellr)\n", "\n", "print(\"修改后模型的全连接层为: \", model.fc)\n", "print(\"使用的损失函数为: \", criterion)\n", "print(\"使用的优化器为: \", optimizer)\n", " "]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "# 定义训练过程\n", "def train(model, device, train_loader, optimizer, epoch):\n", "    model.train()\n", "    sum_loss = 0\n", "    total_num = len(train_loader.dataset)\n", "    # 打印总训练样本数和总iteration数\n", "    print(f'总训练样本数: {total_num}, 总iteration数: {len(train_loader)}')\n", "    for batch_idx, (data, target) in tqdm(enumerate(train_loader)):\n", "        data, target = Variable(data).to(device), Variable(target).to(device)\n", "        # 使用mixup增强数据\n", "#         data, target_a, target_b, lamda = mixup(data, target, alpha=1.0)\n", "        output = model(data)\n", "        # 计算当前iteration的损失函数\n", "        loss = criterion(output, target)\n", "        optimizer.zero_grad()\n", "        # 进行梯度反传\n", "        loss.backward()\n", "        optimizer.step()\n", "        print_loss = loss.data.item()\n", "        sum_loss += print_loss\n", "        # 每50个iteration打印当前的训练进度和损失函数\n", "        if (batch_idx + 1) % 200 == 0:\n", "            print('Train Epoch: {} [{}/{} ({:.0f}%)]\\tLoss: {:.6f}'.format(\n", "                epoch, (batch_idx + 1) * len(data), len(train_loader.dataset),\n", "                       100. * (batch_idx + 1) / len(train_loader), loss.item()))\n", "    # 计算该epoch的平均loss\n", "    ave_loss = sum_loss / len(train_loader)\n", "    print('epoch:{},loss:{}'.format(epoch, ave_loss))\n", " \n", "# 定义验证过程\n", "def val(model, device, test_loader):\n", "    # 将模型设置到预测模式\n", "    model.eval()\n", "    test_loss = 0\n", "    correct = 0\n", "    total_num = len(test_loader.dataset)\n", "    # 打印总测试样本数和总iteration数\n", "    print(f'总测试样本数: {total_num}, 总iteration数: {len(test_loader)}')\n", "    with torch.no_grad():\n", "        for data, target in tqdm(test_loader):\n", "            data, target = Variable(data).to(device), Variable(target).to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "            _, pred = torch.max(output.data, 1)\n", "            # 判断预测结果是否准确，若准确则增加准确预测数\n", "            correct += torch.sum(pred == target)\n", "            print_loss = loss.data.item()\n", "            test_loss += print_loss\n", "        correct = correct.data.item()\n", "        # 计算模型准确率\n", "        acc = correct / total_num\n", "        avgloss = test_loss / len(test_loader)\n", "        print('\\nVal set: Average loss: {:.4f}, Accuracy: {}/{} ({:.0f}%)\\n'.format(\n", "            avgloss, correct, len(test_loader.dataset), 100 * acc))"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["总训练样本数: 10464, 总iteration数: 872\n"]}, {"name": "stderr", "output_type": "stream", "text": ["12it [00:05,  2.04it/s]\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[84], line 7\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# 训练\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;66;03m# 设置随机seed，保证训练集在每个epoch中打乱的顺序相同\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m# seed=10\u001b[39;00m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;66;03m# torch.manual_seed(seed)\u001b[39;00m\n\u001b[0;32m      6\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m epoch \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m1\u001b[39m, EPOCHS \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m):\n\u001b[1;32m----> 7\u001b[0m     \u001b[43mtrain\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mDEVICE\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtrain_loader\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptimizer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mepoch\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      8\u001b[0m     val(model, DEVICE, test_loader)\n\u001b[0;32m      9\u001b[0m \u001b[38;5;66;03m# 保存模型\u001b[39;00m\n", "Cell \u001b[1;32mIn[83], line 13\u001b[0m, in \u001b[0;36mtrain\u001b[1;34m(model, device, train_loader, optimizer, epoch)\u001b[0m\n\u001b[0;32m     10\u001b[0m         data, target \u001b[38;5;241m=\u001b[39m Variable(data)\u001b[38;5;241m.\u001b[39mto(device), Variable(target)\u001b[38;5;241m.\u001b[39mto(device)\n\u001b[0;32m     11\u001b[0m         \u001b[38;5;66;03m# 使用mixup增强数据\u001b[39;00m\n\u001b[0;32m     12\u001b[0m \u001b[38;5;66;03m#         data, target_a, target_b, lamda = mixup(data, target, alpha=1.0)\u001b[39;00m\n\u001b[1;32m---> 13\u001b[0m         output \u001b[38;5;241m=\u001b[39m \u001b[43mmodel\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     14\u001b[0m         \u001b[38;5;66;03m# 计算当前iteration的损失函数\u001b[39;00m\n\u001b[0;32m     15\u001b[0m         loss \u001b[38;5;241m=\u001b[39m criterion(output, target)\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torch\\nn\\modules\\module.py:1553\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1551\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[0;32m   1552\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m-> 1553\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torch\\nn\\modules\\module.py:1562\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1557\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[0;32m   1558\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[0;32m   1559\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[0;32m   1560\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[0;32m   1561\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[1;32m-> 1562\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1564\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m   1565\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torchvision\\models\\resnet.py:285\u001b[0m, in \u001b[0;36mResNet.forward\u001b[1;34m(self, x)\u001b[0m\n\u001b[0;32m    284\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, x: Tensor) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Tensor:\n\u001b[1;32m--> 285\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_forward_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torchvision\\models\\resnet.py:274\u001b[0m, in \u001b[0;36mResNet._forward_impl\u001b[1;34m(self, x)\u001b[0m\n\u001b[0;32m    271\u001b[0m x \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmaxpool(x)\n\u001b[0;32m    273\u001b[0m x \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlayer1(x)\n\u001b[1;32m--> 274\u001b[0m x \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlayer2\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    275\u001b[0m x \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlayer3(x)\n\u001b[0;32m    276\u001b[0m x \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlayer4(x)\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torch\\nn\\modules\\module.py:1553\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1551\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[0;32m   1552\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m-> 1553\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torch\\nn\\modules\\module.py:1562\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1557\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[0;32m   1558\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[0;32m   1559\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[0;32m   1560\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[0;32m   1561\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[1;32m-> 1562\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1564\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m   1565\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torch\\nn\\modules\\container.py:219\u001b[0m, in \u001b[0;36mSequential.forward\u001b[1;34m(self, input)\u001b[0m\n\u001b[0;32m    217\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;28minput\u001b[39m):\n\u001b[0;32m    218\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m module \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m:\n\u001b[1;32m--> 219\u001b[0m         \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[43mmodule\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m    220\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28minput\u001b[39m\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torch\\nn\\modules\\module.py:1553\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1551\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[0;32m   1552\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m-> 1553\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torch\\nn\\modules\\module.py:1562\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1557\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[0;32m   1558\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[0;32m   1559\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[0;32m   1560\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[0;32m   1561\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[1;32m-> 1562\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1564\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m   1565\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torchvision\\models\\resnet.py:96\u001b[0m, in \u001b[0;36mBasicBlock.forward\u001b[1;34m(self, x)\u001b[0m\n\u001b[0;32m     93\u001b[0m out \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbn1(out)\n\u001b[0;32m     94\u001b[0m out \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrelu(out)\n\u001b[1;32m---> 96\u001b[0m out \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconv2\u001b[49m\u001b[43m(\u001b[49m\u001b[43mout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     97\u001b[0m out \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbn2(out)\n\u001b[0;32m     99\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdownsample \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torch\\nn\\modules\\module.py:1553\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1551\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[0;32m   1552\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m-> 1553\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torch\\nn\\modules\\module.py:1562\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1557\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[0;32m   1558\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[0;32m   1559\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[0;32m   1560\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[0;32m   1561\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[1;32m-> 1562\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1564\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m   1565\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torch\\nn\\modules\\conv.py:458\u001b[0m, in \u001b[0;36mConv2d.forward\u001b[1;34m(self, input)\u001b[0m\n\u001b[0;32m    457\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;28minput\u001b[39m: Tensor) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Tensor:\n\u001b[1;32m--> 458\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_conv_forward\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mweight\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbias\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mD:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\torch\\nn\\modules\\conv.py:454\u001b[0m, in \u001b[0;36mConv2d._conv_forward\u001b[1;34m(self, input, weight, bias)\u001b[0m\n\u001b[0;32m    450\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpadding_mode \u001b[38;5;241m!=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mzeros\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[0;32m    451\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m F\u001b[38;5;241m.\u001b[39mconv2d(F\u001b[38;5;241m.\u001b[39mpad(\u001b[38;5;28minput\u001b[39m, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_reversed_padding_repeated_twice, mode\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpadding_mode),\n\u001b[0;32m    452\u001b[0m                     weight, bias, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstride,\n\u001b[0;32m    453\u001b[0m                     _pair(\u001b[38;5;241m0\u001b[39m), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdilation, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgroups)\n\u001b[1;32m--> 454\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mF\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconv2d\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mweight\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbias\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstride\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    455\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpadding\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdilation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgroups\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# 训练\n", "# 设置随机seed，保证训练集在每个epoch中打乱的顺序相同\n", "# seed=10\n", "# torch.manual_seed(seed)\n", "\n", "for epoch in range(1, EPOCHS + 1):\n", "    train(model, DEVICE, train_loader, optimizer, epoch)\n", "    val(model, DEVICE, test_loader)\n", "# 保存模型\n", "torch.save(model, 'model_resnet18.pth')"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["prediction:PNEUMONIA\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 测试结果及可视化\n", "import torch.utils.data.distributed\n", "import torchvision.transforms as transforms\n", " \n", "from torch.autograd import Variable\n", "import os\n", "from PIL import Image\n", "import matplotlib.pyplot as plt\n", "import cv2\n", "\n", "def gray_to_rgb(gray_data):\n", "    rgb_data = np.zeros((3, gray_data.shape[1],gray_data.shape[2]),dtype=np.uint8)\n", "    rgb_data[0, ...]= gray_data\n", "    rgb_data[1, ...]= gray_data\n", "    rgb_data[2, ...]= gray_data\n", "    return torch.tensor(rgb_data)\n", " \n", "classes = ('NORMAL', 'PNEUMONIA')\n", "# 同训练一样进行数据预处理\n", "transform_test_ = transforms.Compose([\n", "    transforms.Resize((224, 224)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize([0.5], [0.5])\n", "])\n", " \n", "DEVICE = torch.device(\"cpu\")\n", "# 读取保存的模型\n", "model = torch.load(\"model_resnet18.pth\")\n", "model.eval()\n", "model.to(DEVICE)\n", "path='data/vis/'\n", "# 根据path读取测试样本列表\n", "testList=os.listdir(path)[:1]\n", "\n", "# 分类样本并可视化\n", "fig = plt.figure()\n", "idx= 0\n", "for file in testList:\n", "    img_ori=Image.open(path+file)\n", "    img=transform_test_(img_ori)\n", "    img = gray_to_rgb(img).float()\n", "    img.unsqueeze_(0)\n", "    img = Variable(img).to(DEVICE)\n", "    out=model(img)\n", "    # 预测分类\n", "    _, pred = torch.max(out.data, 1)\n", "    # 设置包含子图的图像格式\n", "    plt.subplot(1, 3, idx+1)\n", "    #自动调整子图参数，使之填充满整个图像区域\n", "    plt.tight_layout()\n", "    # 展示图像及其预测类别，类别作为子图标题\n", "    plt.imshow(img_ori, interpolation='none')\n", "    plt.title(\"Category:{}\".format(classes[pred.data.item()]))\n", "    plt.xticks([])\n", "    plt.yticks([])\n", "    idx += 1\n", "    # 打印预测结果\n", "    print('prediction:{}'.format(classes[pred.data.item()]))\n", " \n", "    # 展示图象\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["# 获取全连接层的输入\n", "class Identity(nn.Mo<PERSON><PERSON>):\n", "    def __init__(self):\n", "        super(Identity, self).__init__()\n", "\n", "    def forward(self, x):\n", "        return x"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "\n", "classes = ['NORMAL', 'PNEUMONIA']\n", "# 同训练一样进行数据预处理\n", "transform_test = transforms.Compose([\n", "    transforms.Resize((224, 224)),\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])\n", "])\n", " \n", "DEVICE = torch.device(\"cpu\")\n", "# 读取用于可视化的测试集2\n", "path='data/test/'\n", "\n", "dataset_test_ = datasets.ImageFolder(path, transform_test)\n", "test_loader_ = torch.utils.data.DataLoader(dataset_test_, batch_size=BATCH_SIZE, shuffle=False)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["model_cls = torch.load(\"model_resnet18.pth\")\n", "model_cls.eval()\n", "model_cls.to(DEVICE)\n", "\n", "cls = []\n", "with torch.no_grad():\n", "    # for data, target in tqdm(test_loader_):\n", "    for data, target in test_loader_:\n", "        data, target = Variable(data).to(DEVICE), Variable(target).to(DEVICE)\n", "        output = model_cls(data)\n", "        cls.append(output)\n", "cls = torch.cat(cls)\n", "_, cls_pred = torch.max(cls, 1)\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["model_feat = torch.load(\"model_resnet18.pth\")\n", "model_cls.fc = Identity()\n", "model_feat.eval()\n", "model_feat.to(DEVICE)\n", "\n", "feats = []\n", "with torch.no_grad():\n", "    # for data, target in tqdm(test_loader_):\n", "    for data, target in test_loader_:\n", "        data, target = Variable(data).to(DEVICE), Variable(target).to(DEVICE)\n", "        output = model_cls(data)\n", "        feats.append(output)\n", "feats = torch.cat(feats)\n", "# print(feats.shape)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.manifold import TSNE\n", "import seaborn as sns\n", "\n", "tsne = TSNE(n_components=2, init='pca', random_state=0)\n", "# 预测结果可视化\n", "test_feats_emb = tsne.fit_transform(feats)\n", "labels = cls_pred\n", "labels = labels.tolist()\n", "str_labels = []\n", "for label in labels:\n", "    if label == 0:\n", "        label = 'NORMAL'\n", "    else:\n", "        label = 'PNEUMONIA'\n", "    str_labels.append(label)\n", "sns.scatterplot(x=test_feats_emb[:,0], y=test_feats_emb[:,1], hue=str_labels)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python (yolov8)", "language": "python", "name": "yolov8"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}