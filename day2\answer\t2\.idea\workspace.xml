<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="47e270f3-3106-4cc1-a0f5-e64bac03ba03" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/models/yolo/model.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="476">
              <caret line="97" column="33" selection-start-line="97" selection-start-column="33" selection-end-line="97" selection-end-column="33" />
              <folding>
                <element signature="e#69#93#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/nn/tasks.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="-284">
              <caret line="346" column="6" selection-start-line="346" selection-start-column="6" selection-end-line="346" selection-end-column="6" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/nn/modules/head.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="-19227" />
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/ultralytics/examples/README.md">
          <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
            <state split_layout="SPLIT">
              <first_editor />
              <second_editor />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/data/build.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="4063">
              <caret line="263" column="4" selection-start-line="263" selection-start-column="4" selection-end-line="263" selection-end-column="4" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/models/yolo/detect/train.py">
          <provider selected="true" editor-type-id="text-editor" />
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/data/utils.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="6273">
              <caret line="401" column="30" selection-start-line="401" selection-start-column="30" selection-end-line="401" selection-end-column="30" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/utils/checks.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="8551">
              <caret line="549" column="31" selection-start-line="549" selection-start-column="31" selection-end-line="549" selection-end-column="31" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/medicine.yaml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="153">
              <caret line="9" selection-start-line="9" selection-end-line="11" selection-end-column="17" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>meidical-pills.yaml</find>
      <find>medical-pills.yaml</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/detr" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/ultralytics/ultralytics/engine/trainer.py" />
        <option value="$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/medicine.yaml" />
        <option value="$PROJECT_DIR$/medicine.yaml" />
      </list>
    </option>
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="580" />
    <option name="y" value="200" />
    <option name="width" value="1400" />
    <option name="height" value="1000" />
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="t2" type="b2602c69:ProjectViewProjectNode" />
              <item name="t2" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="t2" type="b2602c69:ProjectViewProjectNode" />
              <item name="t2" type="462c0819:PsiDirectoryNode" />
              <item name="ultralytics" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="settings.editor.selected.configurable" value="com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="47e270f3-3106-4cc1-a0f5-e64bac03ba03" name="Default Changelist" comment="" />
      <created>1754600830271</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754600830271</updated>
    </task>
    <servers />
  </component>
  <component name="TodoView">
    <todo-panel id="selected-file">
      <is-autoscroll-to-source value="true" />
    </todo-panel>
    <todo-panel id="all">
      <are-packages-shown value="true" />
      <is-autoscroll-to-source value="true" />
    </todo-panel>
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="2576" height="1416" extended-state="6" />
    <layout>
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.14342001" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Favorites" order="2" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" weight="0.3299532" />
      <window_info anchor="bottom" id="Version Control" order="7" weight="0.3299532" />
      <window_info anchor="bottom" id="Terminal" order="8" sideWeight="0.49960598" visible="true" weight="0.3299532" />
      <window_info anchor="bottom" id="Event Log" order="9" sideWeight="0.500394" side_tool="true" weight="0.3299532" />
      <window_info anchor="bottom" id="Python Console" order="10" weight="0.3299532" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/detection-full-new.ipynb">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/datasets/drug/data.yaml" />
    <entry file="file://$PROJECT_DIR$/datasets/medicine/README.roboflow.txt">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/datasets/medicine/README.dataset.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="68">
          <caret line="4" column="18" lean-forward="true" selection-start-line="4" selection-start-column="18" selection-end-line="4" selection-end-column="18" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/coco.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="238">
          <caret line="14" column="22" selection-start-line="14" selection-end-line="14" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/VOC.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-102" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/mkdocs.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-3825" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/runs/detect/train16/args.yaml" />
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/default.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1581">
          <caret line="123" column="51" lean-forward="true" selection-start-line="123" selection-start-column="51" selection-end-line="123" selection-end-column="51" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/docs/model_data.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-5073" />
      </provider>
    </entry>
    <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/models/yolo/__init__.py">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/medical-pills.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="289">
          <caret line="17" column="9" selection-start-line="17" selection-start-column="2" selection-end-line="17" selection-end-column="9" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/medicine.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="289">
          <caret line="17" selection-start-line="17" selection-end-line="17" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/engine/trainer.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="9503">
          <caret line="606" column="36" selection-start-line="606" selection-start-column="36" selection-end-line="606" selection-end-column="36" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/data/dataset.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="221">
          <caret line="13" column="37" selection-start-line="13" selection-start-column="37" selection-end-line="13" selection-end-column="37" />
        </state>
      </provider>
    </entry>
    <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/data/build.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="4063">
          <caret line="263" column="4" selection-start-line="263" selection-start-column="4" selection-end-line="263" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/models/yolo/detect/train.py">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/data/utils.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="6273">
          <caret line="401" column="30" selection-start-line="401" selection-start-column="30" selection-end-line="401" selection-end-column="30" />
        </state>
      </provider>
    </entry>
    <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/utils/checks.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="8551">
          <caret line="549" column="31" selection-start-line="549" selection-start-column="31" selection-end-line="549" selection-end-column="31" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/medicine.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="153">
          <caret line="9" selection-start-line="9" selection-end-line="11" selection-end-column="17" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/models/yolo/model.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="476">
          <caret line="97" column="33" selection-start-line="97" selection-start-column="33" selection-end-line="97" selection-end-column="33" />
          <folding>
            <element signature="e#69#93#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/nn/tasks.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-284">
          <caret line="346" column="6" selection-start-line="346" selection-start-column="6" selection-end-line="346" selection-end-column="6" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/nn/modules/head.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-19227" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/examples/YOLOv8-Region-Counter/yolov8_region_counter.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-3390" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/examples/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor />
          <second_editor />
        </state>
      </provider>
    </entry>
  </component>
</project>