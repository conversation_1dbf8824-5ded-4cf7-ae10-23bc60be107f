import torch
import torch.nn as nn
from torch.nn.utils.rnn import pad_packed_sequence, pack_padded_sequence


class BiLSTM(nn.Module):
    def __init__(self, vocab_size, emb_size, hidden_size, out_size):
        """初始化参数：
            vocab_size:字典的大小
            emb_size:词向量的维数
            hidden_size：隐向量的维数
            out_size:标注的种类
        """
        super(BiLSTM, self).__init__()
        # <1>定义词向量层，包括词典大小、词向量维度
        self.embedding = _____
        # <2>定义BiLSTM层，包括输入维度、隐藏层维度，再设置batch_first=True、bidirectional=True
        self.bilstm = nn.LSTM(
            _____
            )
        # <3>定义输出全连接层，输入维度（BiLSTM隐藏层维度*2）、输出维度（标签数量）
        self.lin = nn.Linear(
            _____
        )

    def forward(self, sents_tensor, lengths):
        # 实现BiLSTM前向传播
        # <4>词向量化
        emb = _____

        packed = pack_padded_sequence(emb, lengths, batch_first=True)
        # <4>BiLSTM编码
        rnn_out, _ = _____
        rnn_out, _ = pad_packed_sequence(rnn_out, batch_first=True)

        # <4>全连接层输出
        scores = _____
        return scores

    def test(self, sents_tensor, lengths, _):
        """第三个参数不会用到，加它是为了与BiLSTM_CRF保持同样的接口"""
        logits = self.forward(sents_tensor, lengths)  # [B, L, out_size]
        _, batch_tagids = torch.max(logits, dim=2)

        return batch_tagids